"""
Serper.dev Google Search API wrapper for OSINT agents.
"""

import os
import json
import requests
from typing import Dict, List, Any, Optional
from langchain.tools import BaseTool
from pydantic import BaseModel, Field


class SerperSearchInput(BaseModel):
    """Input schema for Serper search tool."""
    query: str = Field(description="Search query to execute")
    num_results: int = Field(default=10, description="Number of results to return")
    time_range: str = Field(default="", description="Time range filter (d1, w1, m1, y1)")
    location: str = Field(default="", description="Geographic location for search")
    search_type: str = Field(default="search", description="Type of search (search, news, images)")


class SerperSearchTool(BaseTool):
    """
    LangChain tool wrapper for Serper.dev Google Search API.

    Provides web search capabilities for OSINT agents with support for
    different search types, time ranges, and geographic filtering.
    """

    name: str = "serper_search"
    description: str = """
    Search the web using Google Search API via Serper.dev.
    Useful for finding recent news, articles, and information on any topic.
    Supports time-based filtering and different search types.
    """
    args_schema: type[BaseModel] = SerperSearchInput
    
    def __init__(self):
        """Initialize the Serper search tool."""
        super().__init__()
        api_key = os.getenv("SERPER_API_KEY")
        if not api_key:
            raise ValueError("SERPER_API_KEY environment variable is required")

        self._api_key = api_key
        self._base_url = "https://google.serper.dev"
        self._headers = {
            "X-API-KEY": self._api_key,
            "Content-Type": "application/json"
        }
    
    def _run(
        self, 
        query: str, 
        num_results: int = 10,
        time_range: str = "",
        location: str = "",
        search_type: str = "search"
    ) -> str:
        """
        Execute a search query using Serper API.
        
        Args:
            query: Search query
            num_results: Number of results to return
            time_range: Time range filter
            location: Geographic location
            search_type: Type of search
            
        Returns:
            JSON string with search results
        """
        try:
            # Prepare search parameters
            params = {
                "q": query,
                "num": min(num_results, 100)  # Serper API limit
            }
            
            # Add optional parameters
            if time_range:
                params["tbs"] = f"qdr:{time_range}"
            if location:
                params["gl"] = location
            
            # Determine endpoint based on search type
            endpoint_map = {
                "search": "/search",
                "news": "/news",
                "images": "/images",
                "videos": "/videos",
                "places": "/places",
                "shopping": "/shopping"
            }

            endpoint = endpoint_map.get(search_type, "/search")
            url = f"{self._base_url}{endpoint}"

            # Make API request
            response = requests.post(
                url,
                headers=self._headers,
                json=params,
                timeout=30
            )
            response.raise_for_status()
            
            # Parse and format results
            data = response.json()
            formatted_results = self._format_results(data, search_type)
            
            return json.dumps(formatted_results, indent=2)
            
        except requests.exceptions.RequestException as e:
            return f"Error making search request: {str(e)}"
        except Exception as e:
            return f"Error processing search results: {str(e)}"
    
    def _format_results(self, data: Dict[str, Any], search_type: str) -> Dict[str, Any]:
        """Format search results for better readability."""
        formatted = {
            "search_type": search_type,
            "total_results": data.get("searchInformation", {}).get("totalResults", 0),
            "search_time": data.get("searchInformation", {}).get("searchTime", 0),
            "results": []
        }
        
        # Format organic results
        if "organic" in data:
            for result in data["organic"]:
                formatted_result = {
                    "title": result.get("title", ""),
                    "link": result.get("link", ""),
                    "snippet": result.get("snippet", ""),
                    "date": result.get("date", ""),
                    "position": result.get("position", 0)
                }
                formatted["results"].append(formatted_result)
        
        # Format news results
        if "news" in data:
            formatted["news"] = []
            for news_item in data["news"]:
                formatted_news = {
                    "title": news_item.get("title", ""),
                    "link": news_item.get("link", ""),
                    "snippet": news_item.get("snippet", ""),
                    "date": news_item.get("date", ""),
                    "source": news_item.get("source", ""),
                    "imageUrl": news_item.get("imageUrl", "")
                }
                formatted["news"].append(formatted_news)
        
        # Add knowledge graph if available
        if "knowledgeGraph" in data:
            kg = data["knowledgeGraph"]
            formatted["knowledge_graph"] = {
                "title": kg.get("title", ""),
                "type": kg.get("type", ""),
                "description": kg.get("description", ""),
                "descriptionSource": kg.get("descriptionSource", ""),
                "descriptionLink": kg.get("descriptionLink", ""),
                "attributes": kg.get("attributes", {})
            }
        
        # Add related searches
        if "relatedSearches" in data:
            formatted["related_searches"] = [
                search.get("query", "") for search in data["relatedSearches"]
            ]
        
        return formatted
    
    async def _arun(self, *args, **kwargs) -> str:
        """Async version of the search tool."""
        # For now, just call the sync version
        # In a production environment, you'd want to use aiohttp
        return self._run(*args, **kwargs)


class SerperNewsSearchTool(SerperSearchTool):
    """Specialized tool for news search."""

    name: str = "serper_news_search"
    description: str = """
    Search for news articles using Google News via Serper.dev.
    Useful for finding recent news and current events on any topic.
    """
    
    def _run(self, query: str, num_results: int = 10, time_range: str = "d1", **kwargs) -> str:
        """Search for news with default time range of 1 day."""
        return super()._run(
            query=query,
            num_results=num_results,
            time_range=time_range,
            search_type="news",
            **kwargs
        )


class SerperImageSearchTool(SerperSearchTool):
    """Specialized tool for image search."""

    name: str = "serper_image_search"
    description: str = """
    Search for images using Google Images via Serper.dev.
    Useful for visual intelligence gathering and verification.
    """
    
    def _run(self, query: str, num_results: int = 10, **kwargs) -> str:
        """Search for images."""
        return super()._run(
            query=query,
            num_results=num_results,
            search_type="images",
            **kwargs
        )
