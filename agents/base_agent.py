"""
Base OSINT Agent class providing common functionality for all specialized agents.
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime

from crewai import Agent, Task, Crew
from crewai.tools import BaseTool
from langchain_openai import Chat<PERSON>penAI
from llama_index.core import VectorStoreIndex, Document
from llama_index.embeddings.openai import OpenAIEmbedding
import dspy

from tools.serper_wrapper import SerperSearchTool
from tools.llamaindex_tools import LlamaIndexRAGTool
from tools.crawl4ai_wrapper import Crawl4AITool
from rag.index_builder import IndexBuilder


class BaseOSINTAgent(ABC):
    """
    Base class for OSINT agents providing common functionality.
    
    Integrates CrewAI, LangChain, LlamaIndex, and DSPy for comprehensive
    intelligence gathering and analysis capabilities.
    """
    
    def __init__(
        self,
        name: str,
        role: str,
        goal: str,
        backstory: str,
        llm_model: str = "gpt-4",
        temperature: float = 0.1,
        max_tokens: int = 2000,
        verbose: bool = True
    ):
        """
        Initialize the base OSINT agent.
        
        Args:
            name: Agent name
            role: Agent role description
            goal: Agent's primary goal
            backstory: Agent's background story
            llm_model: LLM model to use
            temperature: LLM temperature setting
            max_tokens: Maximum tokens for LLM responses
            verbose: Enable verbose logging
        """
        self.name = name
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.verbose = verbose
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            model=llm_model,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        # Initialize DSPy with compatible LM
        try:
            from dspy.clients.openai import OpenAI as DSPyOpenAI
            dspy_lm = DSPyOpenAI(model=llm_model, api_key=os.getenv("OPENAI_API_KEY"))
            dspy.settings.configure(lm=dspy_lm)
        except Exception as e:
            self.logger.warning(f"DSPy configuration failed: {e}. DSPy features may not work.")
        
        # Initialize tools
        self.tools = self._initialize_tools()
        
        # Initialize RAG components
        self.index_builder = IndexBuilder()
        self.vector_index = None
        
        # Initialize CrewAI agent
        self.agent = self._create_crewai_agent()
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO if self.verbose else logging.WARNING,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(self.name)
    
    def _initialize_tools(self) -> List[BaseTool]:
        """Initialize the tools available to this agent."""
        tools = []

        # Add Serper search tool
        if os.getenv("SERPER_API_KEY"):
            tools.append(SerperSearchTool())

        # Add LlamaIndex RAG tool
        tools.append(LlamaIndexRAGTool())

        # Add Crawl4AI tool
        try:
            tools.append(Crawl4AITool())
        except ImportError:
            self.logger.warning("Crawl4AI not available, skipping web crawler tool")

        # Add agent-specific tools
        tools.extend(self._get_specialized_tools())

        return tools
    
    @abstractmethod
    def _get_specialized_tools(self) -> List[BaseTool]:
        """Get tools specific to this agent type."""
        pass
    
    def _create_crewai_agent(self) -> Agent:
        """Create the CrewAI agent instance."""
        return Agent(
            role=self.role,
            goal=self.goal,
            backstory=self.backstory,
            tools=self.tools,
            llm=self.llm,
            verbose=self.verbose,
            allow_delegation=False,
            max_iter=3
        )
    
    def build_knowledge_base(self, documents: List[str], urls: List[str] = None):
        """
        Build a knowledge base from documents and URLs.
        
        Args:
            documents: List of document texts
            urls: Optional list of URLs to crawl and index
        """
        self.logger.info(f"Building knowledge base with {len(documents)} documents")
        
        # Convert texts to LlamaIndex documents
        docs = [Document(text=doc) for doc in documents]
        
        # Build vector index
        self.vector_index = self.index_builder.build_index(docs, urls)
        
        # Update RAG tool with new index
        for tool in self.tools:
            if isinstance(tool, LlamaIndexRAGTool):
                tool.set_index(self.vector_index)
    
    def execute_task(self, task_description: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute a task using the agent.
        
        Args:
            task_description: Description of the task to execute
            context: Optional context information
            
        Returns:
            Dictionary containing task results and metadata
        """
        self.logger.info(f"Executing task: {task_description}")
        
        # Create CrewAI task
        task = Task(
            description=task_description,
            agent=self.agent,
            expected_output="Structured intelligence report with sources and analysis"
        )
        
        # Create crew and execute
        crew = Crew(
            agents=[self.agent],
            tasks=[task],
            verbose=self.verbose
        )
        
        start_time = datetime.now()
        result = crew.kickoff()
        end_time = datetime.now()
        
        # Package results
        return {
            "task": task_description,
            "result": result,
            "agent": self.name,
            "execution_time": (end_time - start_time).total_seconds(),
            "timestamp": end_time.isoformat(),
            "context": context or {}
        }
    
    @abstractmethod
    def analyze(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Perform specialized analysis based on agent type.
        
        Args:
            query: Analysis query
            **kwargs: Additional parameters
            
        Returns:
            Analysis results
        """
        pass
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get information about agent capabilities."""
        return {
            "name": self.name,
            "role": self.role,
            "goal": self.goal,
            "tools": [tool.__class__.__name__ for tool in self.tools],
            "has_knowledge_base": self.vector_index is not None,
            "llm_model": self.llm.model_name if hasattr(self.llm, 'model_name') else "unknown"
        }
